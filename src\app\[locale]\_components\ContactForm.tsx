"use client";

import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { cn } from "@/lib/utils";

const contactFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  whatsapp: z.string().min(1, "WhatsApp number is required"),
});

type ContactFormData = z.infer<typeof contactFormSchema>;

type Props = {
  className?: string;
};

const ContactForm = ({ className }: Props) => {
  const t = useTranslations("contact.form");

  const form = useForm<ContactFormData>({
    resolver: zod<PERSON>esolver(contactFormSchema),
    defaultValues: {
      name: "",
      email: "",
      whatsapp: "",
    },
  });

  const onSubmit = async (data: ContactFormData) => {
    // TODO: Implement form submission logic
    console.log("Form submitted:", data);
  };

  return (
    <article className={cn("flex w-full max-w-2xl flex-col gap-8", className)}>
      <h2 className="text-xl font-medium tracking-wider uppercase lg:text-2xl">
        {t("title")}
      </h2>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-foreground text-lg font-normal">
                  {t("name")}
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    className="border-foreground focus-visible:border-foreground rounded-none border-0 border-b-2 bg-transparent px-0 py-2 text-lg focus-visible:ring-0"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-foreground text-lg font-normal">
                  {t("email")}
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="email"
                    className="border-foreground focus-visible:border-foreground rounded-none border-0 border-b-2 bg-transparent px-0 py-2 text-lg focus-visible:ring-0"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="whatsapp"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-foreground text-lg font-normal">
                  {t("whatsapp")}
                </FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    type="tel"
                    className="border-foreground focus-visible:border-foreground rounded-none border-0 border-b-2 bg-transparent px-0 py-2 text-lg focus-visible:ring-0"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="mt-4 flex justify-end">
            <Button
              type="submit"
              size="lg"
              className="bg-foreground text-background hover:bg-foreground/90 px-8 py-3 text-lg font-medium tracking-wider uppercase"
            >
              {t("send")}
            </Button>
          </div>
        </form>
      </Form>
    </article>
  );
};

export default ContactForm;
